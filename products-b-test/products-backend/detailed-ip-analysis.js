#!/usr/bin/env node

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 目标IP地址
const TARGET_IPS = ['*************', '************'];

// 连接数据库
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

// 产品模型定义
const ProductSchema = new mongoose.Schema({
  productId: String,
  name: mongoose.Schema.Types.Mixed,
  images: {
    front: mongoose.Schema.Types.Mixed,
    back: mongoose.Schema.Types.Mixed,
    label: mongoose.Schema.Types.Mixed,
    package: mongoose.Schema.Types.Mixed,
    gift: mongoose.Schema.Types.Mixed
  }
}, { collection: 'products' });

// 图片模型定义
const ImageSchema = new mongoose.Schema({
  imageId: String,
  productId: String,
  type: String,
  publicUrl: String,
  cdnUrl: String,
  thumbnails: [{
    size: String,
    url: String,
    width: Number,
    height: Number
  }],
  createdAt: Date,
  updatedAt: Date
}, { collection: 'images' });

const Product = mongoose.model('Product', ProductSchema);
const Image = mongoose.model('Image', ImageSchema);

// 详细分析函数
async function performDetailedAnalysis() {
  console.log('\n🔍 执行详细分析...');
  
  const analysis = {
    summary: {},
    productDetails: {},
    imageDetails: {},
    exportData: []
  };

  // 初始化结果结构
  TARGET_IPS.forEach(ip => {
    analysis.summary[ip] = {
      totalCount: 0,
      productCount: 0,
      imageCount: 0,
      fieldBreakdown: {},
      uniqueProducts: new Set(),
      uniqueImages: new Set()
    };
    analysis.productDetails[ip] = [];
    analysis.imageDetails[ip] = [];
  });

  // 分析Product集合
  console.log('📊 分析Product集合...');
  const products = await Product.find({}).lean();
  
  for (const product of products) {
    if (!product.images) continue;

    const imageTypes = ['front', 'back', 'label', 'package', 'gift'];
    
    for (const imageType of imageTypes) {
      const imageData = product.images[imageType];
      if (!imageData) continue;

      let urlToCheck = '';
      if (typeof imageData === 'string') {
        urlToCheck = imageData;
      } else if (typeof imageData === 'object' && imageData.url) {
        urlToCheck = imageData.url;
      }

      if (urlToCheck) {
        TARGET_IPS.forEach(ip => {
          if (urlToCheck.includes(ip)) {
            const fieldName = `products.images.${imageType}`;
            
            analysis.summary[ip].totalCount++;
            analysis.summary[ip].productCount++;
            analysis.summary[ip].uniqueProducts.add(product.productId);
            
            if (!analysis.summary[ip].fieldBreakdown[fieldName]) {
              analysis.summary[ip].fieldBreakdown[fieldName] = 0;
            }
            analysis.summary[ip].fieldBreakdown[fieldName]++;
            
            analysis.productDetails[ip].push({
              productId: product.productId,
              productName: product.name?.display || product.name || 'N/A',
              field: fieldName,
              url: urlToCheck,
              imageType: imageType
            });

            analysis.exportData.push({
              ip: ip,
              collection: 'products',
              productId: product.productId,
              productName: product.name?.display || product.name || 'N/A',
              field: fieldName,
              url: urlToCheck,
              imageType: imageType
            });
          }
        });
      }
    }
  }

  // 分析Image集合
  console.log('📊 分析Image集合...');
  const images = await Image.find({}).lean();
  
  for (const image of images) {
    // 检查publicUrl
    if (image.publicUrl) {
      TARGET_IPS.forEach(ip => {
        if (image.publicUrl.includes(ip)) {
          const fieldName = 'images.publicUrl';
          
          analysis.summary[ip].totalCount++;
          analysis.summary[ip].imageCount++;
          analysis.summary[ip].uniqueImages.add(image.imageId);
          
          if (!analysis.summary[ip].fieldBreakdown[fieldName]) {
            analysis.summary[ip].fieldBreakdown[fieldName] = 0;
          }
          analysis.summary[ip].fieldBreakdown[fieldName]++;
          
          analysis.imageDetails[ip].push({
            imageId: image.imageId,
            productId: image.productId,
            field: fieldName,
            url: image.publicUrl,
            imageType: image.type,
            createdAt: image.createdAt,
            updatedAt: image.updatedAt
          });

          analysis.exportData.push({
            ip: ip,
            collection: 'images',
            imageId: image.imageId,
            productId: image.productId,
            field: fieldName,
            url: image.publicUrl,
            imageType: image.type,
            createdAt: image.createdAt,
            updatedAt: image.updatedAt
          });
        }
      });
    }

    // 检查cdnUrl
    if (image.cdnUrl) {
      TARGET_IPS.forEach(ip => {
        if (image.cdnUrl.includes(ip)) {
          const fieldName = 'images.cdnUrl';
          
          analysis.summary[ip].totalCount++;
          analysis.summary[ip].imageCount++;
          analysis.summary[ip].uniqueImages.add(image.imageId);
          
          if (!analysis.summary[ip].fieldBreakdown[fieldName]) {
            analysis.summary[ip].fieldBreakdown[fieldName] = 0;
          }
          analysis.summary[ip].fieldBreakdown[fieldName]++;
          
          analysis.imageDetails[ip].push({
            imageId: image.imageId,
            productId: image.productId,
            field: fieldName,
            url: image.cdnUrl,
            imageType: image.type,
            createdAt: image.createdAt,
            updatedAt: image.updatedAt
          });

          analysis.exportData.push({
            ip: ip,
            collection: 'images',
            imageId: image.imageId,
            productId: image.productId,
            field: fieldName,
            url: image.cdnUrl,
            imageType: image.type,
            createdAt: image.createdAt,
            updatedAt: image.updatedAt
          });
        }
      });
    }

    // 检查thumbnails
    if (image.thumbnails && Array.isArray(image.thumbnails)) {
      for (const thumbnail of image.thumbnails) {
        if (thumbnail.url) {
          TARGET_IPS.forEach(ip => {
            if (thumbnail.url.includes(ip)) {
              const fieldName = `images.thumbnails.${thumbnail.size}.url`;
              
              analysis.summary[ip].totalCount++;
              analysis.summary[ip].imageCount++;
              analysis.summary[ip].uniqueImages.add(image.imageId);
              
              if (!analysis.summary[ip].fieldBreakdown[fieldName]) {
                analysis.summary[ip].fieldBreakdown[fieldName] = 0;
              }
              analysis.summary[ip].fieldBreakdown[fieldName]++;
              
              analysis.imageDetails[ip].push({
                imageId: image.imageId,
                productId: image.productId,
                field: fieldName,
                url: thumbnail.url,
                imageType: image.type,
                thumbnailSize: thumbnail.size,
                createdAt: image.createdAt,
                updatedAt: image.updatedAt
              });

              analysis.exportData.push({
                ip: ip,
                collection: 'images',
                imageId: image.imageId,
                productId: image.productId,
                field: fieldName,
                url: thumbnail.url,
                imageType: image.type,
                thumbnailSize: thumbnail.size,
                createdAt: image.createdAt,
                updatedAt: image.updatedAt
              });
            }
          });
        }
      }
    }
  }

  // 转换Set为数组长度
  TARGET_IPS.forEach(ip => {
    analysis.summary[ip].uniqueProductCount = analysis.summary[ip].uniqueProducts.size;
    analysis.summary[ip].uniqueImageCount = analysis.summary[ip].uniqueImages.size;
    delete analysis.summary[ip].uniqueProducts;
    delete analysis.summary[ip].uniqueImages;
  });

  return analysis;
}

// 生成详细报告
function generateDetailedReport(analysis) {
  console.log('\n' + '='.repeat(100));
  console.log('📈 详细IP地址使用情况分析报告');
  console.log('='.repeat(100));

  TARGET_IPS.forEach(ip => {
    const summary = analysis.summary[ip];
    
    console.log(`\n🔍 IP地址: ${ip}`);
    console.log('-'.repeat(80));
    
    console.log(`📊 总体统计:`);
    console.log(`   总使用次数: ${summary.totalCount}`);
    console.log(`   Product集合: ${summary.productCount} 次`);
    console.log(`   Image集合: ${summary.imageCount} 次`);
    console.log(`   涉及的唯一产品数: ${summary.uniqueProductCount}`);
    console.log(`   涉及的唯一图片数: ${summary.uniqueImageCount}`);
    
    console.log(`\n📋 字段使用分布:`);
    Object.entries(summary.fieldBreakdown)
      .sort(([,a], [,b]) => b - a)
      .forEach(([field, count]) => {
        console.log(`   ${field}: ${count} 次`);
      });
    
    console.log(`\n💡 示例记录 (前10个):`);
    const examples = [
      ...analysis.productDetails[ip].slice(0, 5),
      ...analysis.imageDetails[ip].slice(0, 5)
    ].slice(0, 10);
    
    examples.forEach((example, index) => {
      console.log(`   ${index + 1}. ${example.field}`);
      console.log(`      URL: ${example.url}`);
      if (example.productId) console.log(`      产品ID: ${example.productId}`);
      if (example.productName) console.log(`      产品名称: ${example.productName}`);
      if (example.imageId) console.log(`      图片ID: ${example.imageId}`);
      if (example.imageType) console.log(`      图片类型: ${example.imageType}`);
      if (example.thumbnailSize) console.log(`      缩略图尺寸: ${example.thumbnailSize}`);
      console.log('');
    });
  });
}

// 导出数据到文件
async function exportData(analysis) {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const exportDir = path.join(__dirname, 'reports');
  
  // 确保目录存在
  if (!fs.existsSync(exportDir)) {
    fs.mkdirSync(exportDir, { recursive: true });
  }
  
  // 导出JSON格式的完整数据
  const jsonFile = path.join(exportDir, `ip-analysis-${timestamp}.json`);
  fs.writeFileSync(jsonFile, JSON.stringify(analysis, null, 2));
  console.log(`📄 完整分析数据已导出到: ${jsonFile}`);
  
  // 导出CSV格式的汇总数据
  const csvFile = path.join(exportDir, `ip-usage-summary-${timestamp}.csv`);
  const csvHeaders = 'IP地址,集合,产品ID,产品名称,图片ID,字段,URL,图片类型,缩略图尺寸,创建时间,更新时间\n';
  const csvData = analysis.exportData.map(row => {
    return [
      row.ip,
      row.collection,
      row.productId || '',
      (row.productName || '').replace(/,/g, '；'),
      row.imageId || '',
      row.field,
      row.url,
      row.imageType || '',
      row.thumbnailSize || '',
      row.createdAt || '',
      row.updatedAt || ''
    ].join(',');
  }).join('\n');
  
  fs.writeFileSync(csvFile, csvHeaders + csvData);
  console.log(`📊 CSV汇总数据已导出到: ${csvFile}`);
}

// 主函数
async function main() {
  try {
    console.log('🚀 开始详细分析数据库中的IP地址使用情况...');
    console.log(`目标IP地址: ${TARGET_IPS.join(', ')}`);
    
    await connectDatabase();
    
    const analysis = await performDetailedAnalysis();
    
    generateDetailedReport(analysis);
    
    await exportData(analysis);
    
    console.log('\n✅ 详细分析完成！');
    
  } catch (error) {
    console.error('❌ 分析过程失败:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { main, performDetailedAnalysis };
