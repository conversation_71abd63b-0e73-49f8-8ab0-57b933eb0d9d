#!/usr/bin/env node

const mongoose = require('mongoose');
require('dotenv').config();

// 目标IP地址
const TARGET_IPS = ['*************', '************'];

// 连接数据库
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

// 产品模型定义（简化版）
const ProductSchema = new mongoose.Schema({
  productId: String,
  name: mongoose.Schema.Types.Mixed,
  images: {
    front: mongoose.Schema.Types.Mixed,
    back: mongoose.Schema.Types.Mixed,
    label: mongoose.Schema.Types.Mixed,
    package: mongoose.Schema.Types.Mixed,
    gift: mongoose.Schema.Types.Mixed
  }
}, { collection: 'products' });

// 图片模型定义（简化版）
const ImageSchema = new mongoose.Schema({
  imageId: String,
  productId: String,
  type: String,
  publicUrl: String,
  cdnUrl: String,
  thumbnails: [{
    size: String,
    url: String,
    width: Number,
    height: Number
  }]
}, { collection: 'images' });

const Product = mongoose.model('Product', ProductSchema);
const Image = mongoose.model('Image', ImageSchema);

// 分析Product集合中的IP使用情况
async function analyzeProductImages() {
  console.log('\n📊 分析Product集合中的图片链接...');
  
  const results = {};
  TARGET_IPS.forEach(ip => {
    results[ip] = {
      count: 0,
      examples: [],
      fields: []
    };
  });

  try {
    const products = await Product.find({}).lean();
    console.log(`总共找到 ${products.length} 个产品记录`);

    for (const product of products) {
      if (!product.images) continue;

      const imageTypes = ['front', 'back', 'label', 'package', 'gift'];
      
      for (const imageType of imageTypes) {
        const imageData = product.images[imageType];
        if (!imageData) continue;

        let urlToCheck = '';
        
        // 处理不同的数据结构
        if (typeof imageData === 'string') {
          urlToCheck = imageData;
        } else if (typeof imageData === 'object' && imageData.url) {
          urlToCheck = imageData.url;
        }

        if (urlToCheck) {
          // 检查每个目标IP
          TARGET_IPS.forEach(ip => {
            if (urlToCheck.includes(ip)) {
              results[ip].count++;
              results[ip].fields.push(`products.images.${imageType}`);
              
              // 保存示例（最多5个）
              if (results[ip].examples.length < 5) {
                results[ip].examples.push({
                  productId: product.productId,
                  field: `images.${imageType}`,
                  url: urlToCheck,
                  name: product.name?.display || product.name || 'N/A'
                });
              }
            }
          });
        }
      }
    }

    return results;
  } catch (error) {
    console.error('❌ 分析Product集合失败:', error);
    return results;
  }
}

// 分析Image集合中的IP使用情况
async function analyzeImageCollection() {
  console.log('\n📊 分析Image集合中的图片链接...');
  
  const results = {};
  TARGET_IPS.forEach(ip => {
    results[ip] = {
      count: 0,
      examples: [],
      fields: []
    };
  });

  try {
    const images = await Image.find({}).lean();
    console.log(`总共找到 ${images.length} 个图片记录`);

    for (const image of images) {
      // 检查publicUrl字段
      if (image.publicUrl) {
        TARGET_IPS.forEach(ip => {
          if (image.publicUrl.includes(ip)) {
            results[ip].count++;
            results[ip].fields.push('images.publicUrl');
            
            if (results[ip].examples.length < 5) {
              results[ip].examples.push({
                imageId: image.imageId,
                productId: image.productId,
                field: 'publicUrl',
                url: image.publicUrl,
                type: image.type
              });
            }
          }
        });
      }

      // 检查cdnUrl字段
      if (image.cdnUrl) {
        TARGET_IPS.forEach(ip => {
          if (image.cdnUrl.includes(ip)) {
            results[ip].count++;
            results[ip].fields.push('images.cdnUrl');
            
            if (results[ip].examples.length < 5) {
              results[ip].examples.push({
                imageId: image.imageId,
                productId: image.productId,
                field: 'cdnUrl',
                url: image.cdnUrl,
                type: image.type
              });
            }
          }
        });
      }

      // 检查thumbnails数组中的url
      if (image.thumbnails && Array.isArray(image.thumbnails)) {
        for (const thumbnail of image.thumbnails) {
          if (thumbnail.url) {
            TARGET_IPS.forEach(ip => {
              if (thumbnail.url.includes(ip)) {
                results[ip].count++;
                results[ip].fields.push(`images.thumbnails.${thumbnail.size}.url`);
                
                if (results[ip].examples.length < 5) {
                  results[ip].examples.push({
                    imageId: image.imageId,
                    productId: image.productId,
                    field: `thumbnails.${thumbnail.size}.url`,
                    url: thumbnail.url,
                    type: image.type
                  });
                }
              }
            });
          }
        }
      }
    }

    return results;
  } catch (error) {
    console.error('❌ 分析Image集合失败:', error);
    return results;
  }
}

// 生成统计报告
function generateReport(productResults, imageResults) {
  console.log('\n' + '='.repeat(80));
  console.log('📈 IP地址使用情况统计报告');
  console.log('='.repeat(80));

  TARGET_IPS.forEach(ip => {
    console.log(`\n🔍 IP地址: ${ip}`);
    console.log('-'.repeat(50));
    
    const productCount = productResults[ip].count;
    const imageCount = imageResults[ip].count;
    const totalCount = productCount + imageCount;
    
    console.log(`📊 总计使用次数: ${totalCount}`);
    console.log(`   - Product集合: ${productCount} 次`);
    console.log(`   - Image集合: ${imageCount} 次`);
    
    // 显示涉及的字段
    const allFields = [...new Set([...productResults[ip].fields, ...imageResults[ip].fields])];
    if (allFields.length > 0) {
      console.log(`\n📋 涉及的字段:`);
      allFields.forEach(field => {
        const productFieldCount = productResults[ip].fields.filter(f => f === field).length;
        const imageFieldCount = imageResults[ip].fields.filter(f => f === field).length;
        const fieldTotal = productFieldCount + imageFieldCount;
        console.log(`   - ${field}: ${fieldTotal} 次`);
      });
    }
    
    // 显示示例
    const allExamples = [...productResults[ip].examples, ...imageResults[ip].examples];
    if (allExamples.length > 0) {
      console.log(`\n💡 示例链接 (显示前5个):`);
      allExamples.slice(0, 5).forEach((example, index) => {
        console.log(`   ${index + 1}. ${example.field}: ${example.url}`);
        if (example.productId) {
          console.log(`      产品ID: ${example.productId}`);
        }
        if (example.name) {
          console.log(`      产品名称: ${example.name}`);
        }
        if (example.type) {
          console.log(`      图片类型: ${example.type}`);
        }
        console.log('');
      });
    }
  });
  
  console.log('\n' + '='.repeat(80));
  console.log('✅ 分析完成');
  console.log('='.repeat(80));
}

// 主函数
async function main() {
  try {
    console.log('🚀 开始分析数据库中的IP地址使用情况...');
    console.log(`目标IP地址: ${TARGET_IPS.join(', ')}`);
    
    await connectDatabase();
    
    const productResults = await analyzeProductImages();
    const imageResults = await analyzeImageCollection();
    
    generateReport(productResults, imageResults);
    
  } catch (error) {
    console.error('❌ 分析过程失败:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { main, analyzeProductImages, analyzeImageCollection };
